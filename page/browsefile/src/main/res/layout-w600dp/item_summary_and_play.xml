<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="itemRecord"
            type="com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/summary_and_play"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/item_play_area"
            layout="@layout/item_play_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp12"
            android:visibility="gone"
            app:itemRecord="@{itemRecord}"
            app:layout_constraintStart_toEndOf="@id/summary_button"
            app:layout_constraintTop_toTopOf="parent" />
        <RelativeLayout
            android:id="@+id/item_play_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp12"
            android:visibility="gone"
            app:itemRecord="@{itemRecord}"
            app:layout_constraintStart_toEndOf="@id/summary_button"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/play_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/ripple_list_play"
                android:src="@drawable/ic_list_play"
                android:tag="play_button" />

        </RelativeLayout>


        <ImageView
            android:id="@+id/summary_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ripple_list_play"
            android:src="@drawable/ic_list_summary"
            android:layout_marginStart="@dimen/dp12"
            android:visibility="gone"
            android:tag="summary_button"
            android:contentDescription="@string/view_summary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>