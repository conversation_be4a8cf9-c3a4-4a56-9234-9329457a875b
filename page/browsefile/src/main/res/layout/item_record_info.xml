<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="itemRecord"
            type="com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/record_title_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.soundrecorder.common.widget.COUIAnimateTextView
                android:id="@+id/record_title"
                style="@style/couiTextAppearanceHeadline6"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="@color/item_record_title_color"
                app:couiAnimateStyle="2"
                app:couiAnimateTextDelay="30"
                app:couiAnimateTextDuration="420"
                app:couiAnimateTextOffset="10"
                app:couiAnimateTextType="7"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="REC20190815112" />

            <com.oplus.anim.EffectiveAnimationView
                android:id="@+id/ai_title_loading"
                android:layout_width="@dimen/dp28"
                android:layout_height="0dp"
                android:forceDarkAllowed="false"
                android:gravity="center_vertical"
                android:visibility="gone"
                app:anim_loop="true"
                app:layout_constraintBottom_toBottomOf="@id/record_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/record_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/record_duration"
            style="@style/couiTextAppearanceArticleBody"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp6"
            android:layout_marginTop="@dimen/dp4"
            android:contentDescription="@{itemRecord.durationContentDescriptionText()}"
            android:ellipsize="end"
            android:fontFamily="sys-sans-en"
            android:fontFeatureSettings="tnum"
            android:singleLine="true"
            android:text="@{itemRecord.durationText()}"
            android:textColor="@color/item_record_extra_info_color"
            android:visibility="visible"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toEndOf="@id/item_convert_loading"
            app:layout_constraintTop_toBottomOf="@id/record_title_container"
            app:layout_goneMarginStart="0dp"
            tools:text="00:53"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_duration_line"
            style="@style/couiTextAppearanceArticleBody"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:fontFamily="sys-sans-en"
            android:fontFeatureSettings="tnum"
            android:singleLine="true"
            android:text=" | "
            android:textColor="@color/item_record_extra_info_color"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/record_duration"
            app:layout_constraintStart_toEndOf="@id/record_duration"
            app:layout_constraintTop_toTopOf="@id/record_duration"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/record_date_modified"
            style="@style/couiTextAppearanceArticleBody"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fontFamily="sys-sans-en"
            android:fontFeatureSettings="tnum"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:text="@{itemRecord.dateModifiedText()}"
            android:textColor="@color/item_record_extra_info_color"
            app:layout_constrainedWidth="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/tv_duration_line"
            app:layout_constraintTop_toTopOf="@id/record_duration"
            app:layout_goneMarginStart="0dp"
            tools:text="2019/05/08"
            tools:visibility="visible" />

        <com.soundrecorder.browsefile.home.view.convert.ConvertLoadingView
            android:id="@+id/item_convert_loading"
            android:layout_width="@dimen/dp18"
            android:layout_height="@dimen/dp18"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp96"
            android:visibility="@{itemRecord.converting ? View.VISIBLE :View.GONE}"
            app:indicatorColor="@color/item_record_convert_loading_color"
            app:layout_constraintBottom_toBottomOf="@id/record_date_modified"
            app:layout_constraintStart_toEndOf="@id/iv_convert_completed"
            app:layout_constraintTop_toTopOf="@id/record_date_modified" />

        <ImageView
            android:id="@+id/iv_convert_completed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingEnd="@dimen/dp6"
            android:src="@drawable/ic_converted_img"
            android:visibility="@{itemRecord.convertCompleted ? View.VISIBLE :View.GONE}"
            app:layout_constraintBottom_toBottomOf="@id/record_date_modified"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/record_date_modified" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>